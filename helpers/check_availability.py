from django.utils import timezone
from datetime import timedelta
from Authentication.models import *
from Project.models import *

def check_user_plan(user_id, subscription_id):
    user = UserRegistration.objects.get(id=user_id)
    
    try:
        subscription = Subscriptions.objects.get(id=subscription_id)
        subscription_data = subscription.subscription_description

        max_posts = subscription_data.get("posts", 0)
        print("max_post",max_posts)
        max_brands = subscription_data.get("no_of_brand", 0)
        max_users = subscription_data.get("no_of_users", 0)
        max_likes_24h = subscription_data.get("no_likes_24h", 0)
        max_comments_24h = subscription_data.get("no_comments_24h", 0)
        social_platforms = subscription_data.get("social_platforms", {}).get("platforms", {})

        user_posts = Post.objects.filter(user_id=user_id, created_at__gte=timezone.now() - timedelta(hours=24)).count()
        user_brands = Brands.objects.filter(user_id=user_id).count()
        user_count = UserManagement.objects.filter(user_id=user_id).count()
        user_likes_24h = LikePost.objects.filter(user_id=user_id, created_on__gte=timezone.now() - timedelta(hours=24)).count()
        user_comments_24h = Comment.objects.filter(user_id=user_id, created_at__gte=timezone.now() - timedelta(hours=24)).count()

        limits_left = {
            "posts_left": "IN" if max_posts == "IN" else (max_posts - user_posts if max_posts else 0),
            "brands_left": "IN" if max_brands == "IN" else (max_brands - user_brands if max_brands else 0),
            "users_left": "IN" if max_users == "IN" else (max_users - user_count if max_users else 0),
            "likes_left_24h": "IN" if max_likes_24h == "IN" else (max_likes_24h - user_likes_24h if max_likes_24h else 0),
            "comments_left_24h": "IN" if max_comments_24h == "IN" else (max_comments_24h - user_comments_24h if max_comments_24h else 0),
        }


        limits_left = {
            key: (True if value == "IN" else (value if value > 0 else False))
            for key, value in limits_left.items()
        }
        social_platform_access = {platform: bool(access) for platform, access in social_platforms.items()}

        return {
            "limits": limits_left,
            "social_platforms": social_platform_access
        }

    except Subscriptions.DoesNotExist:
        limits_left = {
            "posts_left": False,
            "brands_left": False,
            "users_left": False,
            "likes_left_24h":False,
            "comments_left_24h": False,
        }
        return {
            "limits": limits_left,
            "social_platforms": social_platform_access
        }
    
def check_user_posts_24h(brand_id):
    try:
        user_posts_count = Post.objects.filter(
            brand_id=brand_id,
            created_at__gte=timezone.now() - timedelta(hours=24)
        ).count()
        return user_posts_count > 0

    except Post.DoesNotExist:
        return False
    
def check_platform_posts_last_24h(brand_id, validated_data):
    since = timezone.now() - timedelta(hours=24)
    results = {}

    for platform, is_selected in validated_data.items():
        if not is_selected:
            results[platform] = False
            continue

        # Check if the platform field is True in any Post created in the last 24 hours
        filter_kwargs = {
            'user_id': brand_id,
            platform: True,
            'created_at__gte': since
        }

        has_post = Post.objects.filter(**filter_kwargs).exists()

        # If posted → not allowed (False), else allowed (True)
        results[platform] = not has_post

    return results



# def check_role_permission(role_id):
#     try:
#         role = UserRoles.objects.get(id=role_id)
#         role_desc = role.role_description

#         if isinstance(role_desc, dict) and role_desc: 
#             max_role = max(map(int, role_desc.keys()))  
#             return max_role
        
#     except UserRoles.DoesNotExist:
#         pass 

#     return 1


def check_multiple_roles_permissions(user_id, brand_id):
    allowed_role_ids = []

    try:
        str_user_id = int(user_id)
        brand = Brands.objects.get(id=brand_id)
        if str_user_id == brand.user.pk:
            print("str_user_id == brand.user.pk")
            return True, [1,2,3,4,5,6,7]
        if str_user_id not in brand.user_list:
            print("str_user_id not in brand.user_list")
            return False,[]

        try:
            user_management = UserManagement.objects.get(user_invited_id__pk=str_user_id, brand_id=[int(brand_id)])

            try:
                role_obj = UserRoles.objects.get(pk=user_management.permission)
                role_desc = role_obj.role_description 
                print("Role Desc:", role_desc)

                if isinstance(role_desc, dict):
                    for key, value in role_desc.items():
                        print("Key:", key, "Value:", value)
                        is_true = bool(value and str(value).strip())  
                        if is_true:
                            allowed_role_ids.append(int(key))

            except UserRoles.DoesNotExist:
                print("UserRoles DoesNotExist")
                return False, []
            
        except UserManagement.DoesNotExist:
            print("UserManagement DoesNotExist")
            return False, []

    except Brands.DoesNotExist:
        print("Brands DoesNotExist")
        return False , []
    
    print("allowed_role_ids",allowed_role_ids)
    return True,allowed_role_ids


# print(check_multiple_roles_permissions(1,10))
